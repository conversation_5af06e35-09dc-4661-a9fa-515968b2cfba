import * as vscode from 'vscode';

import * as path from 'path';

import * as fs from 'fs-extra';

import {
  WorkspaceContext,
  ProjectGenerationOptions,
  ComponentGenerationOptions,
  GeneratedProject,
  GeneratedComponent,
  GeneratedFile,
  ProjectAnalysis,
  ProjectStructure,
  GeneratedProjectFiles,
  CodeModificationResult,
  ModifiedFile,
  CodeChange,
  FileModification,
  ModificationAnalysis,
  FeatureAdditionResult
} from '../types/ContextTypes';
import { Logger } from '../utils/Logger';

import { AIService } from './AIService';
import { FileOperationsService } from './FileOperationsService';
import { FrontendContextEngine } from './FrontendContextEngine';

/**
 * Advanced Project & Component Generation Service
 * Handles everything from single components to complete project generation
 */
export class ProjectGenerationService {
  constructor(
    private aiService: AIService,
    private contextEngine: FrontendContextEngine,
    private fileOpsService: FileOperationsService
  ) {}

  /**
   * Generate a complete project from description
   * Example: "Create an AI headshot generation project with modern UI"
   */
  async generateCompleteProject(description: string, options: ProjectGenerationOptions): Promise<GeneratedProject> {
    Logger.info(`Starting complete project generation: ${description}`);

    try {
      // 1. Analyze the project requirements
      const projectAnalysis = await this.analyzeProjectRequirements(description);
      
      // 2. Generate project structure
      const projectStructure = await this.generateProjectStructure(projectAnalysis);
      
      // 3. Generate all components and files
      const generatedFiles = await this.generateAllProjectFiles(projectStructure, projectAnalysis);
      
      // 4. Create the project
      const projectPath = await this.createProjectDirectory(projectAnalysis.name, options.targetDirectory);
      
      // 5. Write all files
      await this.writeProjectFiles(projectPath, generatedFiles);
      
      // 6. Install dependencies
      if (options.installDependencies) {
        await this.installProjectDependencies(projectPath, projectAnalysis.dependencies);
      }

      Logger.info(`Complete project generated successfully at: ${projectPath}`);

      return {
        success: true,
        projectPath,
        projectName: projectAnalysis.name,
        framework: projectAnalysis.framework,
        components: generatedFiles.components,
        pages: generatedFiles.pages,
        styles: generatedFiles.styles,
        config: generatedFiles.config,
        dependencies: projectAnalysis.dependencies,
        features: projectAnalysis.features
      };

    } catch (error) {
      Logger.error('Failed to generate complete project:', error);
      throw error;
    }
  }

  /**
   * Generate a single component with advanced features
   */
  async generateAdvancedComponent(description: string, options: ComponentGenerationOptions): Promise<GeneratedComponent> {
    Logger.info(`Generating advanced component: ${description}`);

    try {
      // Get current workspace context
      const context = await this.contextEngine.analyzeWorkspace();
      
      // Analyze component requirements
      const componentAnalysis = await this.analyzeComponentRequirements(description, context);
      
      // Generate component with all features
      const component = await this.generateComponentWithFeatures(componentAnalysis, context);
      
      // Generate supporting files
      const supportingFiles = await this.generateSupportingFiles(component, context);
      
      return {
        ...component,
        supportingFiles,
        animations: componentAnalysis.animations,
        responsiveBreakpoints: componentAnalysis.responsiveBreakpoints,
        accessibility: componentAnalysis.accessibility
      };

    } catch (error) {
      Logger.error('Failed to generate advanced component:', error);
      throw error;
    }
  }

  /**
   * Intelligently modify existing code
   * Example: "Change the navbar color to blue"
   */
  async modifyExistingCode(instruction: string): Promise<CodeModificationResult> {
    Logger.info(`Modifying existing code: ${instruction}`);

    try {
      // 1. Analyze the instruction
      const modificationAnalysis = await this.analyzeModificationInstruction(instruction);
      
      // 2. Find relevant files and code
      const targetFiles = await this.findRelevantFiles(modificationAnalysis);
      
      // 3. Generate modifications for each file
      const modifications: FileModification[] = [];
      
      for (const file of targetFiles) {
        const fileContent = await fs.readFile(file.path, 'utf-8');
        const modification = await this.generateFileModification(
          fileContent,
          file.path,
          modificationAnalysis,
          instruction
        );
        
        if (modification) {
          modifications.push(modification);
        }
      }
      
      // 4. Apply modifications
      const results = await this.applyModifications(modifications);
      
      return {
        success: true,
        instruction,
        modificationsApplied: results.length,
        files: results,
        summary: await this.generateModificationSummary(results)
      };

    } catch (error) {
      Logger.error('Failed to modify existing code:', error);
      throw error;
    }
  }

  /**
   * Add advanced features to existing project
   * Example: "Add GSAP animations to the hero section"
   */
  async addAdvancedFeatures(description: string): Promise<FeatureAdditionResult> {
    Logger.info(`Adding advanced features: ${description}`);

    try {
      // Analyze feature requirements
      const featureAnalysis = await this.analyzeFeatureRequirements(description);
      
      // Generate feature implementation
      const implementation = await this.generateFeatureImplementation(featureAnalysis);
      
      // Apply feature to project
      const result = await this.applyFeatureToProject(implementation);
      
      return result;

    } catch (error) {
      Logger.error('Failed to add advanced features:', error);
      throw error;
    }
  }

  /**
   * Analyze project requirements from description
   */
  private async analyzeProjectRequirements(description: string): Promise<ProjectAnalysis> {
    const prompt = `
Analyze this project description and provide a detailed analysis:

"${description}"

Provide a JSON response with:
{
  "name": "project-name",
  "type": "web-app|landing-page|dashboard|e-commerce|portfolio|saas",
  "framework": "react|vue|angular|svelte",
  "features": ["feature1", "feature2"],
  "components": ["component1", "component2"],
  "pages": ["page1", "page2"],
  "styling": "tailwind|styled-components|css-modules|sass",
  "animations": ["gsap", "framer-motion", "css-animations"],
  "dependencies": ["dep1", "dep2"],
  "complexity": "simple|medium|complex",
  "estimatedFiles": 15,
  "description": "Detailed project description"
}

Focus on modern, professional implementations with:
- Responsive design
- Accessibility
- Performance optimization
- Modern UI/UX patterns
- Professional animations
`;

    const response = await this.aiService.processMessage(prompt);
    
    try {
      return JSON.parse(response.content);
    } catch (error) {
      Logger.error('Failed to parse project analysis:', error);
      throw new Error('Invalid project analysis response');
    }
  }

  /**
   * Generate project structure
   */
  private async generateProjectStructure(analysis: ProjectAnalysis): Promise<ProjectStructure> {
    const prompt = `
Generate a complete project structure for: ${analysis.name}

Project Details:
- Type: ${analysis.type}
- Framework: ${analysis.framework}
- Features: ${analysis.features.join(', ')}
- Styling: ${analysis.styling}

Provide a JSON response with the complete folder structure:
{
  "src": {
    "components": {
      "ui": ["Button.tsx", "Input.tsx"],
      "layout": ["Header.tsx", "Footer.tsx"],
      "features": ["FeatureComponent.tsx"]
    },
    "pages": ["Home.tsx", "About.tsx"],
    "styles": ["globals.css", "components.css"],
    "utils": ["helpers.ts", "constants.ts"],
    "hooks": ["useCustomHook.ts"],
    "context": ["AppContext.tsx"],
    "assets": {
      "images": [],
      "icons": []
    }
  },
  "public": ["index.html", "favicon.ico"],
  "config": ["package.json", "tsconfig.json", "tailwind.config.js"]
}

Make it production-ready with proper organization.
`;

    const response = await this.aiService.processMessage(prompt);
    
    try {
      return JSON.parse(response.content);
    } catch (error) {
      Logger.error('Failed to parse project structure:', error);
      throw new Error('Invalid project structure response');
    }
  }

  /**
   * Generate all project files
   */
  private async generateAllProjectFiles(
    structure: ProjectStructure,
    analysis: ProjectAnalysis
  ): Promise<GeneratedProjectFiles> {
    const files: GeneratedProjectFiles = {
      components: [],
      pages: [],
      styles: [],
      config: [],
      utils: [],
      assets: []
    };

    // Generate components
    for (const componentPath of this.flattenStructure(structure.src.components)) {
      const component = await this.generateSingleFile(componentPath, 'component', analysis);
      files.components.push(component);
    }

    // Generate pages
    for (const pagePath of structure.src.pages) {
      const page = await this.generateSingleFile(pagePath, 'page', analysis);
      files.pages.push(page);
    }

    // Generate styles
    for (const stylePath of structure.src.styles) {
      const style = await this.generateSingleFile(stylePath, 'style', analysis);
      files.styles.push(style);
    }

    // Generate config files
    for (const configPath of structure.config) {
      const config = await this.generateSingleFile(configPath, 'config', analysis);
      files.config.push(config);
    }

    return files;
  }

  /**
   * Generate a single file based on type and context
   */
  private async generateSingleFile(
    filePath: string,
    fileType: 'component' | 'page' | 'style' | 'config' | 'util',
    analysis: ProjectAnalysis
  ): Promise<GeneratedFile> {
    const fileName = path.basename(filePath, path.extname(filePath));
    
    const prompt = `
Generate a ${fileType} file: ${filePath}

Project Context:
- Name: ${analysis.name}
- Type: ${analysis.type}
- Framework: ${analysis.framework}
- Styling: ${analysis.styling}
- Features: ${analysis.features.join(', ')}

Requirements:
- Modern, professional code
- TypeScript if applicable
- Responsive design
- Accessibility features
- Performance optimized
- Clean, maintainable code
- Proper error handling
- Professional animations where appropriate

${fileType === 'component' ? 'Include props interface, proper typing, and modern patterns.' : ''}
${fileType === 'page' ? 'Include proper layout, SEO meta tags, and responsive design.' : ''}
${fileType === 'style' ? 'Include modern CSS patterns, responsive breakpoints, and smooth animations.' : ''}

Provide only the file content, no explanations.
`;

    const response = await this.aiService.processMessage(prompt);
    
    return {
      path: filePath,
      content: response.content,
      type: fileType
    };
  }

  /**
   * Analyze component requirements
   */
  private async analyzeComponentRequirements(
    description: string,
    context: WorkspaceContext
  ): Promise<ComponentAnalysis> {
    const prompt = `
Analyze this component request in the context of an existing ${context.framework.name} project:

"${description}"

Current project context:
- Framework: ${context.framework.name}
- Styling: ${context.styles.framework}
- Existing components: ${context.components.map(c => c.name).join(', ')}

Provide a JSON response with:
{
  "name": "ComponentName",
  "type": "functional|class|composition",
  "props": [{"name": "prop1", "type": "string", "required": true}],
  "features": ["responsive", "animated", "accessible"],
  "animations": [{"type": "gsap", "trigger": "scroll", "duration": 1000}],
  "responsiveBreakpoints": [{"name": "mobile", "minWidth": 320}],
  "accessibility": {"ariaLabels": {}, "keyboardNavigation": true},
  "dependencies": ["gsap", "framer-motion"],
  "complexity": "simple|medium|complex",
  "description": "Detailed component description"
}

Focus on modern, professional implementation with smooth animations and accessibility.
`;

    const response = await this.aiService.processMessage(prompt);

    try {
      return JSON.parse(response.content);
    } catch (error) {
      Logger.error('Failed to parse component analysis:', error);
      throw new Error('Invalid component analysis response');
    }
  }

  /**
   * Generate component with all advanced features
   */
  private async generateComponentWithFeatures(
    analysis: ComponentAnalysis,
    context: WorkspaceContext
  ): Promise<GeneratedComponent> {
    const prompt = `
Generate a complete ${context.framework.name} component: ${analysis.name}

Requirements:
${analysis.description}

Component Details:
- Type: ${analysis.type}
- Props: ${JSON.stringify(analysis.props)}
- Features: ${analysis.features.join(', ')}
- Animations: ${JSON.stringify(analysis.animations)}
- Framework: ${context.framework.name}
- Styling: ${context.styles.framework}

Generate:
1. Main component code with TypeScript
2. Styled component/CSS module
3. Animation implementation (GSAP/Framer Motion)
4. Responsive breakpoints
5. Accessibility features
6. Props interface
7. Usage examples

Make it production-ready with:
- Clean, maintainable code
- Proper error handling
- Performance optimization
- Modern patterns
- Professional animations
- Full accessibility support

Provide JSON response:
{
  "code": "component code here",
  "styles": "styles here",
  "documentation": "usage documentation",
  "props": [prop definitions],
  "animations": [animation configs],
  "accessibility": accessibility config
}
`;

    const response = await this.aiService.processMessage(prompt);

    try {
      const parsed = JSON.parse(response.content);
      return {
        id: analysis.name.toLowerCase(),
        name: analysis.name,
        ...parsed
      };
    } catch (error) {
      Logger.error('Failed to parse component generation:', error);
      throw new Error('Invalid component generation response');
    }
  }

  /**
   * Analyze modification instruction
   */
  private async analyzeModificationInstruction(instruction: string): Promise<ModificationAnalysis> {
    const prompt = `
Analyze this code modification instruction:

"${instruction}"

Provide a JSON response with:
{
  "intent": "style-change|component-update|feature-addition|bug-fix|refactor",
  "targetElements": ["navbar", "button", "header"],
  "properties": {"color": "blue", "background": "#007acc"},
  "scope": "file|component|project",
  "confidence": 0.9
}

Focus on understanding what needs to be changed and where.
`;

    const response = await this.aiService.processMessage(prompt);

    try {
      return JSON.parse(response.content);
    } catch (error) {
      Logger.error('Failed to parse modification analysis:', error);
      throw new Error('Invalid modification analysis response');
    }
  }

  /**
   * Find relevant files for modification
   */
  private async findRelevantFiles(analysis: ModificationAnalysis): Promise<RelevantFile[]> {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
      return [];
    }

    const relevantFiles: RelevantFile[] = [];

    // Search for files containing target elements
    for (const element of analysis.targetElements) {
      const searchPattern = `**/*.{tsx,jsx,ts,js,vue,svelte}`;
      const files = await vscode.workspace.findFiles(searchPattern, '**/node_modules/**');

      for (const file of files) {
        try {
          const content = await fs.readFile(file.fsPath, 'utf-8');

          // Check if file contains the target element
          if (this.containsTargetElement(content, element)) {
            relevantFiles.push({
              path: file.fsPath,
              confidence: this.calculateFileRelevance(content, element),
              element
            });
          }
        } catch (error) {
          Logger.warn(`Failed to read file ${file.fsPath}:`, error);
        }
      }
    }

    // Sort by confidence and return top matches
    return relevantFiles
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 5);
  }

  /**
   * Generate file modification
   */
  private async generateFileModification(
    fileContent: string,
    filePath: string,
    analysis: ModificationAnalysis,
    instruction: string
  ): Promise<FileModification | null> {
    const prompt = `
Modify this file based on the instruction:

Instruction: "${instruction}"
File: ${path.basename(filePath)}
Target: ${analysis.targetElements.join(', ')}
Properties: ${JSON.stringify(analysis.properties)}

Current file content:
\`\`\`
${fileContent}
\`\`\`

Generate the modified file content that implements the requested changes.
Make minimal, precise changes while maintaining code quality.

Provide JSON response:
{
  "modifiedContent": "complete modified file content",
  "changes": [
    {
      "type": "modification",
      "lineNumber": 15,
      "oldContent": "old code",
      "newContent": "new code",
      "description": "Changed navbar color to blue"
    }
  ],
  "confidence": 0.9
}
`;

    const response = await this.aiService.processMessage(prompt);

    try {
      const parsed = JSON.parse(response.content);
      return {
        filePath,
        originalContent: fileContent,
        modifiedContent: parsed.modifiedContent,
        changes: parsed.changes,
        confidence: parsed.confidence
      };
    } catch (error) {
      Logger.error('Failed to parse file modification:', error);
      return null;
    }
  }

  // Additional helper methods...
  private flattenStructure(obj: Record<string, string[]>): string[] {
    const result: string[] = [];
    for (const [key, value] of Object.entries(obj)) {
      if (Array.isArray(value)) {
        result.push(...value.map(v => `${key}/${v}`));
      }
    }
    return result;
  }

  private containsTargetElement(content: string, element: string): boolean {
    const patterns = [
      new RegExp(`class.*${element}`, 'i'),
      new RegExp(`id.*${element}`, 'i'),
      new RegExp(`${element}.*component`, 'i'),
      new RegExp(`const.*${element}`, 'i'),
      new RegExp(`function.*${element}`, 'i')
    ];

    return patterns.some(pattern => pattern.test(content));
  }

  private calculateFileRelevance(content: string, element: string): number {
    let score = 0;

    // Higher score for exact matches
    if (content.toLowerCase().includes(element.toLowerCase())) {
      score += 0.5;
    }

    // Higher score for component definitions
    if (content.includes(`const ${element}`) || content.includes(`function ${element}`)) {
      score += 0.3;
    }

    // Higher score for class/id attributes
    if (content.includes(`className="${element}"`) || content.includes(`id="${element}"`)) {
      score += 0.2;
    }

    return Math.min(1.0, score);
  }

  private async createProjectDirectory(name: string, targetDir?: string): Promise<string> {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    const baseDir = targetDir || workspaceFolder?.uri.fsPath || process.cwd();
    const projectPath = path.join(baseDir, name);

    await fs.ensureDir(projectPath);
    return projectPath;
  }

  private async writeProjectFiles(projectPath: string, files: GeneratedProjectFiles): Promise<void> {
    const allFiles = [
      ...files.components,
      ...files.pages,
      ...files.styles,
      ...files.config,
      ...files.utils,
      ...files.assets
    ];

    for (const file of allFiles) {
      const fullPath = path.join(projectPath, file.path);
      await fs.ensureDir(path.dirname(fullPath));
      await fs.writeFile(fullPath, file.content, 'utf-8');
    }
  }

  private async installProjectDependencies(projectPath: string, dependencies: string[]): Promise<void> {
    // This would typically run npm install or yarn install
    Logger.info(`Would install dependencies: ${dependencies.join(', ')}`);
  }

  private async generateSupportingFiles(component: GeneratedComponent, context: WorkspaceContext): Promise<GeneratedFile[]> {
    // Generate tests, storybook, etc.
    return [];
  }

  private async generateFeatureImplementation(analysis: any): Promise<any> {
    // Implementation for adding features
    return {};
  }

  private async applyFeatureToProject(implementation: any): Promise<FeatureAdditionResult> {
    // Apply feature to existing project
    return {
      success: true,
      feature: '',
      filesModified: [],
      filesCreated: [],
      dependencies: [],
      instructions: []
    };
  }

  private async analyzeFeatureRequirements(description: string): Promise<any> {
    // Analyze what features to add
    return {};
  }

  private async applyModifications(modifications: FileModification[]): Promise<ModifiedFile[]> {
    const results: ModifiedFile[] = [];

    for (const mod of modifications) {
      try {
        // Apply the modification
        await fs.writeFile(mod.filePath, mod.modifiedContent, 'utf-8');

        results.push({
          path: mod.filePath,
          originalContent: mod.originalContent,
          modifiedContent: mod.modifiedContent,
          changes: mod.changes
        });
      } catch (error) {
        Logger.error(`Failed to apply modification to ${mod.filePath}:`, error);
      }
    }

    return results;
  }

  private async generateModificationSummary(results: ModifiedFile[]): Promise<string> {
    return `Successfully modified ${results.length} files with ${results.reduce((sum, r) => sum + r.changes.length, 0)} changes.`;
  }
}

// Additional type definitions
interface ComponentAnalysis {
  name: string;
  type: 'functional' | 'class' | 'composition';
  props: any[];
  features: string[];
  animations: any[];
  responsiveBreakpoints: any[];
  accessibility: any;
  dependencies: string[];
  complexity: 'simple' | 'medium' | 'complex';
  description: string;
}

interface RelevantFile {
  path: string;
  confidence: number;
  element: string;
}
