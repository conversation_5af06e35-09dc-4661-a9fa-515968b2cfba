import * as vscode from 'vscode';
import { ServiceRegistry } from '../core/ServiceRegistry';
import { Logger } from '../utils/Logger';
import { AIService } from '../services/AIService';
import { ConfigurationService } from '../services/ConfigurationService';
import { ProjectDetectionService } from '../services/ProjectDetectionService';
import { UsageTrackingService } from '../services/UsageTrackingService';

/**
 * Chat webview provider for UIOrbit extension
 * Provides the main chat interface for user interaction
 */
export class ChatWebviewProvider implements vscode.WebviewViewProvider {
  public static readonly viewType = 'uiorbit.chatView';

  private _view?: vscode.WebviewView;
  private aiService: AIService;

  constructor(
    private readonly _extensionUri: vscode.Uri,
    private readonly serviceRegistry: ServiceRegistry
  ) {
    // Initialize AI service
    const configService = this.serviceRegistry.getRequired<ConfigurationService>('configuration');
    this.aiService = new AIService(configService);

    // Inject project detection service if available
    const projectDetectionService = this.serviceRegistry.get<ProjectDetectionService>('projectDetection');
    if (projectDetectionService) {
      this.aiService.setProjectDetectionService(projectDetectionService);
    }
  }

  /**
   * Resolve the webview view
   */
  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken,
  ) {
    Logger.info('ChatWebviewProvider.resolveWebviewView called');
    this._view = webviewView;

    webviewView.webview.options = {
      // Allow scripts in the webview
      enableScripts: true,
      localResourceRoots: [
        this._extensionUri
      ]
    };

    const html = this._getHtmlForWebview(webviewView.webview);
    Logger.info('Setting webview HTML, length:', html.length);
    webviewView.webview.html = html;

    // Handle messages from the webview
    webviewView.webview.onDidReceiveMessage(
      message => {
        Logger.info('Received message from webview:', message);
        this.handleMessage(message);
      },
      undefined,
      []
    );

    // Send welcome message after a delay to ensure React app is loaded
    setTimeout(() => {
      Logger.info('Sending welcome message to webview');
      this.postMessage({
        type: 'welcome',
        text: 'Welcome to UIOrbit! How can I help you with frontend development today?'
      });
    }, 2000);

    Logger.info('Chat webview resolved successfully');
  }

  /**
   * Handle messages from the webview
   */
  private async handleMessage(message: any): Promise<void> {
    try {
      Logger.debug('Received message from webview:', message);

      switch (message.type) {
        case 'chat-message':
          await this.handleChatMessage(message.text);
          break;
        case 'ready':
          await this.handleWebviewReady();
          break;
        case 'webview-loaded':
          Logger.info('Webview loaded successfully');
          break;
        case 'get-usage-stats':
          await this.handleGetUsageStats();
          break;
        case 'show-usage-details':
          await this.handleShowUsageDetails();
          break;
        case 'error':
          Logger.error('Webview error:', message.error);
          break;
        default:
          Logger.warn('Unknown message type:', message.type);
      }
    } catch (error) {
      Logger.error('Error handling webview message:', error);
    }
  }

  /**
   * Handle chat message from user
   */
  private async handleChatMessage(text: string): Promise<void> {
    if (!text || text.trim().length === 0) {
      return;
    }

    Logger.info('Processing chat message:', text);

    // Send typing indicator
    this.postMessage({
      type: 'assistant-typing',
      isTyping: true
    });

    try {
      // Check if AI service is ready
      if (!this.aiService.isReady()) {
        // Send configuration message
        this.postMessage({
          type: 'assistant-message',
          text: '🔧 **AI Configuration Required**\n\nTo enable intelligent responses, please configure your OpenAI API key:\n\n1. Open VS Code Settings (`Ctrl+,` or `Cmd+,`)\n2. Search for "UIOrbit"\n3. Enter your OpenAI API key in **"UIOrbit > OpenAI API Key"**\n\nOnce configured, I\'ll be able to provide expert frontend development assistance! 🚀',
          timestamp: new Date().toISOString(),
          isError: true
        });
        return;
      }

      // Process message with AI service
      const aiResponse = await this.aiService.processMessage(text);

      // Send response
      this.postMessage({
        type: 'assistant-message',
        text: aiResponse.content,
        timestamp: new Date().toISOString(),
        isError: aiResponse.isError
      });

      // Log usage if available
      if (aiResponse.usage) {
        Logger.info(`AI Response - Tokens used: ${aiResponse.usage.totalTokens} (prompt: ${aiResponse.usage.promptTokens}, completion: ${aiResponse.usage.completionTokens})`);
      }

      // Update usage stats in UI
      await this.handleGetUsageStats();

    } catch (error) {
      Logger.error('Error processing chat message:', error);
      
      this.postMessage({
        type: 'assistant-message',
        text: 'Sorry, I encountered an error processing your message. Please try again.',
        timestamp: new Date().toISOString(),
        isError: true
      });
    } finally {
      // Stop typing indicator
      this.postMessage({
        type: 'assistant-typing',
        isTyping: false
      });
    }
  }

  /**
   * Handle webview ready event
   */
  private async handleWebviewReady(): Promise<void> {
    Logger.info('Webview is ready');

    // Update project context
    await this.aiService.updateProjectContext();
    const projectContext = this.aiService.getProjectContext();

    // Generate context-aware welcome message
    let welcomeMessage = 'Hello! I\'m UIOrbit, your AI-powered frontend development assistant. ';

    if (projectContext.projectType && projectContext.projectType !== 'unknown' && projectContext.projectType !== 'none') {
      welcomeMessage += `I can see you're working on a ${projectContext.projectType} project. `;
    }

    welcomeMessage += 'I can help you with:\n\n';
    welcomeMessage += '• 🎨 Component design and architecture\n';
    welcomeMessage += '• 🚀 Modern UI/UX best practices\n';
    welcomeMessage += '• 📱 Responsive design solutions\n';
    welcomeMessage += '• ⚡ Performance optimization\n';
    welcomeMessage += '• 🎭 Animations and interactions\n';
    welcomeMessage += '• 🔧 Debugging and troubleshooting\n\n';
    welcomeMessage += 'What would you like to work on today?';

    // Send welcome message
    this.postMessage({
      type: 'assistant-message',
      text: welcomeMessage,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle get usage stats request
   */
  private async handleGetUsageStats(): Promise<void> {
    try {
      const usageService = this.serviceRegistry.get<UsageTrackingService>('usageTracking');
      if (usageService) {
        const stats = await usageService.getUsageStats();
        this.postMessage({
          type: 'usage-stats',
          stats: stats
        });
      }
    } catch (error) {
      Logger.error('Error getting usage stats:', error);
    }
  }

  /**
   * Handle show usage details request
   */
  private async handleShowUsageDetails(): Promise<void> {
    try {
      const usageService = this.serviceRegistry.get<UsageTrackingService>('usageTracking');
      if (usageService) {
        await usageService.showUsageStats();
      }
    } catch (error) {
      Logger.error('Error showing usage details:', error);
    }
  }

  /**
   * Post message to webview
   */
  private postMessage(message: any): void {
    if (this._view) {
      this._view.webview.postMessage(message);
    }
  }

  /**
   * Get HTML content for the webview with enhanced HTML/CSS/JS
   */
  private _getHtmlForWebview(webview: vscode.Webview): string {
    // Use enhanced HTML version - React was causing issues
    const useReact = false; // Keep it simple with enhanced HTML/CSS/JS

    if (!useReact) {
      // Enhanced HTML version with all Week 2 features
      return this._getEnhancedHtmlForWebview(webview);
    }

    // Get URIs for React bundle
    const scriptUri = webview.asWebviewUri(
      vscode.Uri.joinPath(this._extensionUri, 'dist', 'webview.js')
    );

    // Use a nonce to only allow specific scripts to be run
    const nonce = this.getNonce();

    Logger.info('Script URI:', scriptUri.toString());

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}' 'unsafe-eval';">
    <title>UIOrbit Chat</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
        }
        #root {
            height: 100vh;
        }
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            font-size: 14px;
            color: var(--vscode-descriptionForeground);
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">Loading UIOrbit Chat...</div>
    </div>
    <script nonce="${nonce}">
        console.log('UIOrbit webview script loading...');
        console.log('Script URI: ${scriptUri}');

        // Add error handling
        window.addEventListener('error', function(e) {
            console.error('UIOrbit webview error:', e.error);
        });

        // Check if VS Code API is available
        try {
            const vscode = acquireVsCodeApi();
            console.log('VS Code API acquired successfully');
            vscode.postMessage({ type: 'webview-loaded' });
        } catch (error) {
            console.error('Failed to acquire VS Code API:', error);
        }
    </script>
    <script nonce="${nonce}" src="${scriptUri}" onload="console.log('React script loaded successfully')" onerror="console.error('Failed to load React script')"></script>
</body>
</html>`;
  }

  /**
   * Get enhanced HTML content with Augment-level design
   */
  private _getEnhancedHtmlForWebview(webview: vscode.Webview): string {
    const nonce = this.getNonce();

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
    <title>UIOrbit Chat</title>
    <style>
        /* Augment-inspired design system */
        :root {
            --uiorbit-primary: #007acc;
            --uiorbit-primary-hover: #005a9e;
            --uiorbit-secondary: #6c757d;
            --uiorbit-success: #28a745;
            --uiorbit-warning: #ffc107;
            --uiorbit-error: #dc3545;
            --uiorbit-border-radius: 8px;
            --uiorbit-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            --uiorbit-transition: all 0.2s ease;
        }

        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        /* Augment-style header */
        .header {
            background: linear-gradient(135deg, var(--vscode-sideBar-background) 0%, var(--vscode-editor-background) 100%);
            border-bottom: 1px solid var(--vscode-panel-border);
            padding: 16px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 60px;
            box-shadow: var(--uiorbit-shadow);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--uiorbit-primary) 0%, #4a90e2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--vscode-foreground);
            margin: 0;
        }

        .header-subtitle {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin: 0;
            margin-top: 2px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            background-color: var(--vscode-badge-background);
            color: var(--vscode-badge-foreground);
        }

        .usage-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 500;
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            color: var(--vscode-foreground);
            cursor: pointer;
            transition: var(--uiorbit-transition);
        }

        .usage-indicator:hover {
            background-color: var(--vscode-list-hoverBackground);
        }

        .usage-bar {
            width: 40px;
            height: 4px;
            background-color: var(--vscode-input-border);
            border-radius: 2px;
            overflow: hidden;
        }

        .usage-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--uiorbit-success) 0%, var(--uiorbit-warning) 70%, var(--uiorbit-error) 100%);
            transition: width 0.3s ease;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: var(--uiorbit-success);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Enhanced chat container */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 16px;
            overflow-y: auto;
            padding: 20px;
            background-color: var(--vscode-editor-background);
        }
        /* Augment-style messages */
        .message {
            padding: 16px 20px;
            border-radius: var(--uiorbit-border-radius);
            max-width: 85%;
            word-wrap: break-word;
            position: relative;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: var(--uiorbit-transition);
        }

        .message:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .user-message {
            background: linear-gradient(135deg, var(--uiorbit-primary) 0%, #4a90e2 100%);
            color: white;
            align-self: flex-end;
            border-bottom-right-radius: 4px;
        }

        .user-message::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: -8px;
            width: 0;
            height: 0;
            border: 8px solid transparent;
            border-bottom-color: var(--uiorbit-primary);
            border-right: 0;
            border-bottom-right-radius: 4px;
        }

        .assistant-message {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            align-self: flex-start;
            border-bottom-left-radius: 4px;
        }

        .assistant-message::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: -8px;
            width: 0;
            height: 0;
            border: 8px solid transparent;
            border-bottom-color: var(--vscode-input-background);
            border-left: 0;
            border-bottom-left-radius: 4px;
        }
        /* Augment-style input area */
        .input-container {
            background-color: var(--vscode-sideBar-background);
            border-top: 1px solid var(--vscode-panel-border);
            padding: 16px 20px;
            display: flex;
            gap: 12px;
            align-items: flex-end;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 2px solid var(--vscode-input-border);
            border-radius: var(--uiorbit-border-radius);
            padding: 12px 16px;
            font-family: inherit;
            font-size: inherit;
            resize: none;
            min-height: 20px;
            max-height: 120px;
            transition: var(--uiorbit-transition);
            box-sizing: border-box;
        }

        .message-input:focus {
            outline: none;
            border-color: var(--uiorbit-primary);
            box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.1);
        }

        .message-input::placeholder {
            color: var(--vscode-input-placeholderForeground);
            font-style: italic;
        }

        .send-button {
            background: linear-gradient(135deg, var(--uiorbit-primary) 0%, #4a90e2 100%);
            color: white;
            border: none;
            border-radius: var(--uiorbit-border-radius);
            padding: 12px 20px;
            cursor: pointer;
            font-family: inherit;
            font-size: inherit;
            font-weight: 600;
            transition: var(--uiorbit-transition);
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 80px;
            justify-content: center;
        }

        .send-button:hover:not(:disabled) {
            background: linear-gradient(135deg, var(--uiorbit-primary-hover) 0%, #3a7bc8 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 122, 204, 0.3);
        }

        .send-button:active:not(:disabled) {
            transform: translateY(0);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .send-icon {
            font-size: 14px;
        }
        .status {
            font-style: italic;
            opacity: 0.7;
            padding: 8px 12px;
            text-align: center;
        }

        /* Enhanced Week 2 Features */
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 12px;
            max-width: 85%;
            align-self: flex-start;
            animation: fadeIn 0.3s ease-in;
            margin-bottom: 12px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background-color: var(--vscode-foreground);
            border-radius: 50%;
            opacity: 0.4;
            animation: typingDot 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        .typing-dot:nth-child(3) { animation-delay: 0s; }

        @keyframes typingDot {
            0%, 80%, 100% {
                opacity: 0.4;
                transform: scale(1);
            }
            40% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.error {
            background-color: var(--vscode-inputValidation-errorBackground);
            border: 1px solid var(--vscode-inputValidation-errorBorder);
            color: var(--vscode-inputValidation-errorForeground);
        }

        .message-timestamp {
            font-size: 11px;
            opacity: 0.6;
            margin-top: 4px;
            text-align: right;
        }

        .assistant-message .message-timestamp {
            text-align: left;
        }

        /* Code block styling */
        .code-block {
            background-color: var(--vscode-textCodeBlock-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 6px;
            margin: 8px 0;
            overflow: hidden;
        }

        .code-header {
            background-color: var(--vscode-sideBar-background);
            padding: 8px 12px;
            border-bottom: 1px solid var(--vscode-input-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
        }

        .code-content {
            padding: 12px;
            font-family: var(--vscode-editor-font-family);
            font-size: 13px;
            line-height: 1.4;
            overflow-x: auto;
        }

        .copy-button {
            background: none;
            border: none;
            color: var(--vscode-foreground);
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            transition: background-color 0.2s ease;
        }

        .copy-button:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }

        /* Enhanced animations */
        .message {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- Augment-style header -->
    <div class="header">
        <div class="header-left">
            <div class="header-logo">🚀</div>
            <div>
                <h1 class="header-title">UIOrbit</h1>
                <p class="header-subtitle">AI Frontend Development Assistant</p>
            </div>
        </div>
        <div class="header-right">
            <div class="usage-indicator" id="usageIndicator" onclick="showUsageDetails()" title="Click to view usage details">
                <span id="usageText">--/100</span>
                <div class="usage-bar">
                    <div class="usage-fill" id="usageFill" style="width: 0%"></div>
                </div>
            </div>
            <div class="status-indicator" id="statusIndicator">
                <div class="status-dot"></div>
                <span id="status">Ready</span>
            </div>
        </div>
    </div>

    <!-- Enhanced chat container -->
    <div class="chat-container" id="chatContainer">
        <!-- Messages will be added here dynamically -->
    </div>

    <!-- Augment-style input area -->
    <div class="input-container">
        <div class="input-wrapper">
            <textarea
                id="messageInput"
                class="message-input"
                placeholder="Ask me anything about frontend development... (Ctrl+Enter to send)"
                rows="1"
            ></textarea>
        </div>
        <button id="sendButton" class="send-button">
            <span class="send-icon">➤</span>
            <span>Send</span>
        </button>
    </div>

    <script nonce="${nonce}">
        console.log('UIOrbit simple webview loaded');

        const vscode = acquireVsCodeApi();
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const status = document.getElementById('status');
        let isTyping = false;

        // Send ready message
        vscode.postMessage({ type: 'webview-loaded' });
        vscode.postMessage({ type: 'ready' });

        // Request initial usage stats
        vscode.postMessage({ type: 'get-usage-stats' });

        // Handle send button click
        sendButton.addEventListener('click', sendMessage);

        // Handle keyboard shortcuts
        messageInput.addEventListener('keydown', (e) => {
            // Ctrl+Enter or Cmd+Enter to send
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
            }
            // Shift+Enter for new line (default behavior)
            // Enter alone for new line (changed from send to be more user-friendly)
        });

        // Auto-resize textarea
        messageInput.addEventListener('input', () => {
            messageInput.style.height = 'auto';
            messageInput.style.height = messageInput.scrollHeight + 'px';
        });

        function sendMessage() {
            console.log('Send button clicked!');
            const text = messageInput.value.trim();
            console.log('Message text:', text);

            if (!text || isTyping) {
                console.log('Message empty or typing in progress');
                return;
            }

            // Add user message to chat
            addMessage(text, 'user');

            // Send to extension
            console.log('Sending message to extension:', text);
            vscode.postMessage({
                type: 'chat-message',
                text: text
            });

            // Clear input
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Show typing status
            setTyping(true);
        }

        function addMessage(text, sender, isError = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + sender + '-message' + (isError ? ' error' : '');

            // Create message content with timestamp
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            // Process text for code blocks and formatting
            const processedContent = processMessageContent(text);
            messageContent.innerHTML = processedContent;

            // Add timestamp
            const timestamp = document.createElement('div');
            timestamp.className = 'message-timestamp';
            timestamp.textContent = new Date().toLocaleTimeString();

            messageDiv.appendChild(messageContent);
            messageDiv.appendChild(timestamp);

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function processMessageContent(text) {
            // Process code blocks
            let processed = text.replace(/\`\`\`(\\w+)?\\n([\\s\\S]*?)\`\`\`/g, function(match, language, code) {
                const lang = language || 'text';
                return '<div class="code-block">' +
                    '<div class="code-header">' +
                    '<span>' + lang + '</span>' +
                    '<button class="copy-button" onclick="copyCode(this)">Copy</button>' +
                    '</div>' +
                    '<div class="code-content"><pre><code>' + escapeHtml(code.trim()) + '</code></pre></div>' +
                    '</div>';
            });

            // Process inline code
            processed = processed.replace(/\`([^\`]+)\`/g, '<code style="background-color: var(--vscode-textCodeBlock-background); padding: 2px 4px; border-radius: 3px;">$1</code>');

            // Process line breaks
            processed = processed.replace(/\\n/g, '<br>');

            return processed;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function copyCode(button) {
            const codeContent = button.closest('.code-block').querySelector('code').textContent;
            navigator.clipboard.writeText(codeContent).then(() => {
                button.textContent = 'Copied!';
                setTimeout(() => {
                    button.textContent = 'Copy';
                }, 2000);
            });
        }

        function setTyping(typing) {
            isTyping = typing;
            sendButton.disabled = typing;

            // Update status with enhanced styling
            const statusIndicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('status');

            if (typing) {
                statusText.textContent = 'Thinking...';
                statusIndicator.style.backgroundColor = 'var(--uiorbit-warning)';
                statusIndicator.style.color = 'white';
                sendButton.innerHTML = '<span class="send-icon">⏳</span><span>Sending...</span>';
            } else {
                statusText.textContent = 'Ready';
                statusIndicator.style.backgroundColor = 'var(--vscode-badge-background)';
                statusIndicator.style.color = 'var(--vscode-badge-foreground)';
                sendButton.innerHTML = '<span class="send-icon">➤</span><span>Send</span>';
            }

            // Show/hide typing indicator
            const existingIndicator = document.querySelector('.typing-indicator');
            if (typing && !existingIndicator) {
                showTypingIndicator();
            } else if (!typing && existingIndicator) {
                hideTypingIndicator();
            }
        }

        function showTypingIndicator() {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'typing-indicator';
            typingDiv.innerHTML =
                '<span>UIOrbit is thinking</span>' +
                '<div class="typing-dots">' +
                    '<div class="typing-dot"></div>' +
                    '<div class="typing-dot"></div>' +
                    '<div class="typing-dot"></div>' +
                '</div>';

            chatContainer.appendChild(typingDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            const indicator = document.querySelector('.typing-indicator');
            if (indicator) {
                indicator.remove();
            }
        }

        function showUsageDetails() {
            vscode.postMessage({ type: 'show-usage-details' });
        }

        function updateUsageDisplay(stats) {
            const usageText = document.getElementById('usageText');
            const usageFill = document.getElementById('usageFill');
            const usageIndicator = document.getElementById('usageIndicator');

            if (usageText && usageFill && usageIndicator) {
                usageText.textContent = stats.current + '/' + stats.limit;
                usageFill.style.width = stats.percentage + '%';

                // Update colors based on usage
                if (stats.percentage >= 90) {
                    usageIndicator.style.borderColor = 'var(--uiorbit-error)';
                    usageIndicator.style.color = 'var(--uiorbit-error)';
                } else if (stats.percentage >= 70) {
                    usageIndicator.style.borderColor = 'var(--uiorbit-warning)';
                    usageIndicator.style.color = 'var(--uiorbit-warning)';
                } else {
                    usageIndicator.style.borderColor = 'var(--vscode-input-border)';
                    usageIndicator.style.color = 'var(--vscode-foreground)';
                }

                // Update tooltip
                usageIndicator.title = 'Usage: ' + stats.current + '/' + stats.limit + ' requests this month. Resets: ' + new Date(stats.resetDate).toLocaleDateString();
            }
        }

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            console.log('Received message:', message);

            switch (message.type) {
                case 'assistant-message':
                    addMessage(message.text, 'assistant', message.isError);
                    setTyping(false);
                    break;
                case 'assistant-typing':
                    setTyping(message.isTyping);
                    break;
                case 'welcome':
                    status.textContent = 'Connected and ready!';
                    break;
                case 'usage-stats':
                    updateUsageDisplay(message.stats);
                    break;
            }
        });

        console.log('UIOrbit webview initialized successfully');
    </script>
</body>
</html>`;
  }

  /**
   * Generate a nonce for CSP
   */
  private getNonce(): string {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }
}
