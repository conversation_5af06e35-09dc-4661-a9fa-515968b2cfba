/**
 * Type definitions for the Frontend Context Engine
 */

export interface WorkspaceContext {
  framework: FrameworkInfo;
  components: ComponentInfo[];
  styles: StyleAnalysis;
  dependencies: DependencyMap;
  designSystem: DesignTokens;
  patterns: Pattern[];
  performance: PerformanceAnalysis;
  accessibility: AccessibilityAnalysis;
  structure: ProjectStructure;
  lastUpdated: Date;
}

export interface FrameworkInfo {
  name: string;
  version: string;
  features: string[];
  confidence: number;
  ecosystem?: string[];
}

export interface ComponentInfo {
  id: string;
  name: string;
  filePath: string;
  type: 'functional' | 'class' | 'composition' | 'directive';
  framework: string;
  props: PropInfo[];
  exports: ExportInfo[];
  imports: ImportInfo[];
  dependencies: string[];
  description?: string;
  tags: string[];
  complexity: number;
  lines: number;
  lastModified: Date;
}

export interface PropInfo {
  name: string;
  type: string;
  required: boolean;
  defaultValue?: string;
  description?: string;
}

export interface ExportInfo {
  name: string;
  type: 'default' | 'named';
  kind: 'component' | 'function' | 'constant' | 'type';
}

export interface ImportInfo {
  source: string;
  imports: string[];
  type: 'default' | 'named' | 'namespace';
}

export interface StyleAnalysis {
  framework: string;
  files: number;
  patterns: StylePattern[];
  colors: ColorToken[];
  typography: TypographyToken[];
  spacing: SpacingToken[];
  breakpoints: BreakpointToken[];
  animations: AnimationToken[];
}

export interface StylePattern {
  name: string;
  type: 'utility' | 'component' | 'layout' | 'theme';
  usage: number;
  files: string[];
}

export interface DesignTokens {
  colors: ColorToken[];
  typography: TypographyToken[];
  spacing: SpacingToken[];
  shadows: ShadowToken[];
  borderRadius: BorderRadiusToken[];
  breakpoints: BreakpointToken[];
  animations: AnimationToken[];
}

export interface ColorToken {
  name: string;
  value: string;
  type: 'primary' | 'secondary' | 'accent' | 'neutral' | 'semantic';
  usage: number;
  files: string[];
}

export interface TypographyToken {
  name: string;
  fontFamily: string;
  fontSize: string;
  fontWeight: string;
  lineHeight?: string;
  letterSpacing?: string;
  usage: number;
  files: string[];
}

export interface SpacingToken {
  name: string;
  value: string;
  type: 'margin' | 'padding' | 'gap' | 'inset';
  usage: number;
  files: string[];
}

export interface ShadowToken {
  name: string;
  value: string;
  type: 'box' | 'text' | 'drop';
  usage: number;
  files: string[];
}

export interface BorderRadiusToken {
  name: string;
  value: string;
  usage: number;
  files: string[];
}

export interface BreakpointToken {
  name: string;
  value: string;
  type: 'min-width' | 'max-width' | 'range';
  usage: number;
  files: string[];
}

export interface AnimationToken {
  name: string;
  value: string;
  type: 'transition' | 'keyframe' | 'transform';
  duration?: string;
  easing?: string;
  usage: number;
  files: string[];
}

export interface DependencyMap {
  production: Record<string, string>;
  development: Record<string, string>;
  peer: Record<string, string>;
}

export interface Pattern {
  name: string;
  type: 'architectural' | 'design' | 'performance' | 'accessibility';
  description: string;
  examples: string[];
  confidence: number;
  benefits: string[];
  drawbacks?: string[];
}

export interface PerformanceAnalysis {
  score: number;
  issues: PerformanceIssue[];
  suggestions: PerformanceSuggestion[];
}

export interface PerformanceIssue {
  type: 'bundle-size' | 'unused-deps' | 'large-components' | 'inefficient-imports';
  severity: 'low' | 'medium' | 'high';
  description: string;
  files: string[];
  impact: string;
}

export interface PerformanceSuggestion {
  type: 'optimization' | 'refactoring' | 'tooling';
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  files?: string[];
}

export interface AccessibilityAnalysis {
  score: number;
  issues: AccessibilityIssue[];
  suggestions: AccessibilitySuggestion[];
}

export interface AccessibilityIssue {
  type: 'missing-alt' | 'no-aria-labels' | 'color-contrast' | 'keyboard-nav' | 'semantic-html';
  severity: 'low' | 'medium' | 'high';
  description: string;
  files: string[];
  wcagLevel: 'A' | 'AA' | 'AAA';
}

export interface AccessibilitySuggestion {
  type: 'improvement' | 'best-practice' | 'tooling';
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  files?: string[];
  wcagLevel: 'A' | 'AA' | 'AAA';
}

export interface ProjectStructure {
  depth: number;
  files: number;
  directories: number;
  patterns: StructurePattern[];
}

export interface StructurePattern {
  name: string;
  type: 'feature-based' | 'layer-based' | 'domain-based' | 'atomic';
  confidence: number;
  description: string;
}

export interface RelevantContext {
  components: ComponentInfo[];
  patterns: Pattern[];
  dependencies: string[];
  designTokens: DesignTokens;
  suggestions: ContextSuggestion[];
}

export interface ContextSuggestion {
  type: 'component' | 'pattern' | 'library' | 'optimization';
  title: string;
  description: string;
  code?: string;
  confidence: number;
  tags: string[];
}

export type DependencyGraph = Map<string, string[]>;

export interface FileAnalysis {
  path: string;
  type: 'component' | 'style' | 'config' | 'test' | 'asset';
  framework?: string;
  size: number;
  complexity: number;
  dependencies: string[];
  exports: string[];
  lastModified: Date;
}

export interface CodebaseMetrics {
  totalFiles: number;
  totalLines: number;
  componentCount: number;
  testCoverage?: number;
  bundleSize?: number;
  dependencies: {
    total: number;
    outdated: number;
    vulnerable: number;
  };
  complexity: {
    average: number;
    highest: number;
    distribution: Record<string, number>;
  };
}

export interface TrendAnalysis {
  patterns: TrendPattern[];
  technologies: TechnologyTrend[];
  recommendations: TrendRecommendation[];
  lastUpdated: Date;
}

export interface TrendPattern {
  name: string;
  category: 'design' | 'architecture' | 'performance' | 'accessibility';
  popularity: number;
  adoption: 'emerging' | 'growing' | 'mainstream' | 'declining';
  description: string;
  examples: string[];
  benefits: string[];
  considerations: string[];
}

export interface TechnologyTrend {
  name: string;
  category: 'framework' | 'library' | 'tool' | 'language';
  popularity: number;
  growth: number;
  description: string;
  alternatives: string[];
  migrationPath?: string;
}

export interface TrendRecommendation {
  type: 'adopt' | 'trial' | 'assess' | 'hold';
  technology: string;
  reasoning: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  timeline: string;
}
